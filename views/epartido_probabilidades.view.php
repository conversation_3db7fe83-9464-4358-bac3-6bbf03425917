<?php
#region region DOCS
/** @var string $id_partido */
/** @var Partido $cur_partido */
/** @var Pais[] $paises */
/** @var array $paises_torneos_info */
/** @var PartidoTorneo[] $partido_torneos */
/** @var PartidoInfo[] $partidos_infos */
/** @var int $tabselected */
/** @var int $n_partidos_primer_corte */
/** @var int $n_partidos_segundo_corte */
/** @var int $n_row_partidos_infos */
/** @var float $prob_total_goles_masde_1_5_p1_home_h */
/** @var float $prob_total_goles_masde_1_5_p2_home_h */
/** @var float $prob_total_goles_masde_1_5_p1_away_a */
/** @var float $prob_total_goles_masde_1_5_p2_away_a */
/** @var float $prob_total_goles_masde_1_5_p1_home */
/** @var float $prob_total_goles_masde_1_5_p2_home */
/** @var float $prob_total_goles_masde_1_5_p1_away */
/** @var float $prob_total_goles_masde_1_5_p2_away */
/** @var float $prob_home_marca_p1_home_h */
/** @var float $prob_home_marca_p2_home_h */
/** @var float $prob_home_marca_p1_home */
/** @var float $prob_home_marca_p2_home */
/** @var float $prob_home_superior_1_5_p1_home_h */
/** @var float $prob_home_superior_1_5_p2_home_h */
/** @var float $prob_home_superior_1_5_p1_home */
/** @var float $prob_home_superior_1_5_p2_home */
/** @var float $prob_away_marca_p1_away_a */
/** @var float $prob_away_marca_p2_away_a */
/** @var float $prob_away_marca_p1_away */
/** @var float $prob_away_marca_p2_away */
/** @var float $prob_away_superior_1_5_p1_away_a */
/** @var float $prob_away_superior_1_5_p2_away_a */
/** @var float $prob_away_superior_1_5_p1_away */
/** @var float $prob_away_superior_1_5_p2_away */
/** @var float $prob_ambos_marcan_p1_home_h */
/** @var float $prob_ambos_marcan_p2_home_h */
/** @var float $prob_ambos_marcan_p1_away_a */
/** @var float $prob_ambos_marcan_p2_away_a */
/** @var float $prob_ambos_marcan_p1_home */
/** @var float $prob_ambos_marcan_p2_home */
/** @var float $prob_ambos_marcan_p1_away */
/** @var float $prob_ambos_marcan_p2_away */
/** @var float $avg_goals_p1_home */
/** @var float $avg_goals_p2_home */
/** @var float $avg_goals_p1_away */
/** @var float $avg_goals_p2_away */
/** @var float $avg_conceded_p1_home */
/** @var float $avg_conceded_p2_home */
/** @var float $avg_conceded_p1_away */
/** @var float $avg_conceded_p2_away */
/** @var float $min_porc_viable */
/** @var int $numero_por_revisar */
/** @var PartidoTorneo[] $partidos_torneos_grouped */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>My Dash | Probabilidades</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<link href="<?php echo RUTA ?>resources/assets/plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
	<?php #region region CSS grouped controls ?>
	<link href="<?php echo RUTA ?>resources/css/grouped_controls.css" rel="stylesheet" />
	<?php #endregion CSS grouped controls ?>


	
	<style>
	/* Match Header Enhancements */
	.match-header-card {
		background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
		border: 1px solid #404040;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	}

	.match-header-card .card-body {
		background: rgba(255, 255, 255, 0.02);
		backdrop-filter: blur(10px);
	}

	.vs-circle {
		background: linear-gradient(45deg, #007bff, #0056b3);
		box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
		transition: transform 0.3s ease;
	}

	.vs-circle:hover {
		transform: scale(1.1);
	}

	.team-name {
		transition: color 0.3s ease, text-shadow 0.3s ease;
        margin-top: 15px;
	}

	.team-name:hover {
		color: #007bff !important;
		text-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
	}

	.match-detail-box {
		background: rgba(108, 117, 125, 0.15);
		border: 1px solid rgba(255, 255, 255, 0.1);
		transition: all 0.3s ease;
		height: 70px;
	}

	.match-detail-box:hover {
		background: rgba(108, 117, 125, 0.25);
		border-color: rgba(255, 255, 255, 0.2);
		transform: translateY(-2px);
	}

	.countdown-badge {
		animation: pulse 2s infinite;
	}

	@keyframes pulse {
		0% { opacity: 1; }
		50% { opacity: 0.7; }
		100% { opacity: 1; }
	}

	/* Action buttons enhancement */
	.btn-outline-light:hover, .btn-outline-info:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
		color: black !important;
	}

	/* Historical table separators */
	.historical-separator-5 {
		border-bottom: 2px solid #007bff !important;
	}

	.historical-separator-10 {
		border-bottom: 2px solid #28a745 !important;
	}
	</style>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<h4>Ver probabilidades</h4>

		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FORM ?>
		<form action="epartido_probabilidades" method="POST">
			<input type="hidden" id="id_partido" name="id_partido" value="<?php echo @recover_var($id_partido) ?>">
			<input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">

			<!-- BEGIN Match Header Card -->
			<div class="card match-header-card mt-3 shadow-lg">
				<div class="card-body p-4">
					<!-- Match Teams Row -->
					<div class="row align-items-center mb-3">
						<div class="col-md-5">
							<div class="text-center">
								<div class="badge bg-primary mb-2 fs-10px">HOME</div>
								<h4 class="text-white mb-0 fw-bold cursor-pointer team-name" id="home" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
									<i class="fas fa-home me-2 text-primary"></i><?php echo @recover_var($cur_partido->home) ?>
								</h4>
							</div>
						</div>
						<div class="col-md-2">
							<div class="text-center">
								<div class="vs-circle rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
									<span class="text-white fw-bold fs-18px">VS</span>
								</div>
							</div>
						</div>
						<div class="col-md-5">
							<div class="text-center">
								<div class="badge bg-warning mb-2 fs-10px">AWAY</div>
								<h4 class="text-white mb-0 fw-bold cursor-pointer team-name" id="away" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
									<?php echo @recover_var($cur_partido->away) ?><i class="fas fa-plane ms-2 text-warning"></i>
								</h4>
							</div>
						</div>
					</div>

					<!-- Match Details Row -->
					<div class="row">
						<div class="col-md-6 mb-3 mb-md-0">
							<div class="d-flex align-items-center match-detail-box rounded p-3">
								<i class="fas fa-trophy text-success me-3 fs-18px"></i>
								<div>
									<small class="text-muted d-block">Tournament</small>
									<span class="text-white fw-semibold cursor-pointer" id="torneo" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
										<?php echo @recover_var($cur_partido->pais_torneo->nombre) ?>
									</span>
								</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="d-flex align-items-center match-detail-box rounded p-3">
								<i class="fas fa-calendar-alt text-info me-3 fs-18px"></i>
								<div class="flex-grow-1">
									<small class="text-muted d-block">Match Date & Time</small>
									<span class="text-white fw-semibold cursor-pointer" id="fecha_hora" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
										<?php
										// Display both date and time
										if (!empty($cur_partido->fecha) && !empty($cur_partido->horamilitar)) {
											echo $cur_partido->fecha . ' ' . format_date_to_hora_24_format_HHMM($cur_partido->horamilitar);
										}
										?>
									</span>
									<!-- Countdown display -->
									<div class="mt-1" id="countdown_container" style="display: none;">
										<div class="badge bg-info countdown-badge fs-10px">
											<i class="fas fa-clock me-1"></i>
											<span id="countdown_text"></span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- END Match Header Card -->
			<!-- BEGIN Action Buttons Row -->
			<div class="row mt-3">
				<?php #region region LINK regresar ?>
				<div class="col-md-4 col-xs-12 mb-2">
					<a href="lpartidos" class="btn btn-outline-light btn-sm w-100 d-flex align-items-center justify-content-center">
						<i class="fas fa-list me-2"></i>Ir a listado de partidos
					</a>
				</div>
				<?php #endregion LINK regresar ?>
				<?php #region region LINK ir a listado de probabilidades ?>
				<div class="col-md-4 col-xs-12 mb-2">
					<a href="lpartidos_probabilidades" class="btn btn-outline-info btn-sm w-100 d-flex align-items-center justify-content-center">
						<i class="fas fa-chart-line me-2"></i>Ir a listado de probabilidades
					</a>
				</div>
				<?php #endregion LINK ir a listado de probabilidades ?>
				<?php #region region SUBMIT sub_refrescar ?>
				<div class="col-md-4 col-xs-12 mb-2">
					<button type="submit" id="sub_refrescar" name="sub_refrescar" class="btn btn-primary btn-sm w-100 d-flex align-items-center justify-content-center">
						<i class="fas fa-sync-alt me-2"></i>Refrescar
					</button>
				</div>
				<?php #endregion SUBMIT sub_refrescar ?>
			</div>
			<!-- END Action Buttons Row -->
			<?php #region region PANEL torneos ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<div class="col-md-4 col-xs-12">
						<h4 class="panel-title">
							Torneos incluidos:
						</h4>
					</div>
					<!-- BEGIN buttons section -->
					<div class="col-md-8 col-xs-12">
						<div class="d-flex gap-2 justify-content-end">
							<button type="button" class="btn btn-xs btn-success" onclick="showCreateTournamentModal()">
								<i class="fas fa-plus me-1"></i>Create New
							</button>
							<button type="button" class="btn btn-xs btn-primary" onclick="showAddTournamentDropdown()">
								<i class="fas fa-plus-circle me-1"></i>Add Tournament
							</button>
						</div>
						<!-- Tournament dropdown (initially hidden) -->
						<div id="tournament_dropdown_container" class="mt-2" style="display: none;">
							<div class="input-group">
	                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
	                                Select:
	                            </span>
								<select id="id_pais_dropdown" class="default-select2 form-control form-control-fh form-select2-fh fs-12px no-border-radious">
									<option value="">--</option>
									<?php foreach ($paises_torneos_info as $pais): ?>
										<option value="<?php echo limpiar_datos($pais['idpais']); ?>">
											<?php echo limpiar_datos($pais['torneo']); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<button type="button" class="btn btn-xs btn-success" onclick="addSelectedTournament()">
									<i class="fas fa-check me-1"></i>Add
								</button>
								<button type="button" class="btn btn-xs btn-secondary" onclick="hideAddTournamentDropdown()">
									<i class="fas fa-times me-1"></i>Cancel
								</button>
							</div>
						</div>
					</div>
					<!-- END buttons section -->
				</div>
				<!-- BEGIN PANEL body -->
				<div class="p-1 table-nowrap" style="overflow: auto">
					<?php #region region TABLE torneos ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="text-center">Acciones</th>
							<th class="text-center">Nombre</th>
							<th class="text-center">Temporada</th>
							<th class="text-center">Fecha actualizado</th>
						</tr>
						</thead>
						<tbody class="fs-12px">
						<?php foreach ($partidos_torneos_grouped as $partido_torneo): ?>
							<tr class="cursor-pointer">
								<td class="text-center align-middle">
									<div class="d-flex gap-1 justify-content-center">
										<button type="button" class="btn btn-xs btn-primary"
											onclick="openUploadModal('<?php echo $partido_torneo->pais->id; ?>', '<?php echo addslashes($partido_torneo->pais->nombre); ?>', '<?php echo $partido_torneo->season; ?>')"
											title="Cargar datos para este torneo">
											<i class="fas fa-upload me-1"></i>Cargar datos
										</button>
										<button type="button" class="btn btn-xs btn-warning"
											onclick="openEditTournamentModal('<?php echo $partido_torneo->pais->id; ?>', '<?php echo addslashes($partido_torneo->pais->nombre); ?>')"
											title="Editar torneo">
											<i class="fas fa-edit me-1"></i>Edit
										</button>
									</div>
								</td>
								<td class="align-middle"><?php echo $partido_torneo->pais->nombre; ?></td>
								<td class="text-center align-middle"><?php echo $partido_torneo->season; ?></td>
								<td class="text-center align-middle">
									<?php
									echo $partido_torneo->fecha_upload;
									// Add days ago calculation
									if (!empty($partido_torneo->fecha_upload)) {
										setTimeZoneCol(); // Set Bogotá timezone
										$fecha_actual = create_date();
										$days_diff = getDateDiffDaysNonLiteral($partido_torneo->fecha_upload, $fecha_actual);
										if ($days_diff > 0) {
											echo ' <small class="text-muted">(' . $days_diff . ' days ago)</small>';
										} elseif ($days_diff == 0) {
											echo ' <small class="text-success">(today)</small>';
										}
									}
									?>
								</td>
							</tr>
						<?php endforeach; ?>
						</tbody>
					</table>
					<?php #endregion TABLE torneos ?>
				</div>
				<!-- END PANEL body -->
			</div>
			<?php #endregion PANEL torneos ?>
			<?php #region region NAVTAB HEAD analisis ?>
			<ul class="region_NAVTAB_HEAD nav nav-tabs fs-13px mt-3 no-border-radious">
				<li class="nav-item" onclick="tabselect(1)">
					<a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">
						Total SUPERIOR 1.5
						<span id="porc_acierto_1_5_badge" class="badge bg-primary rounded-0 fs-12px ms-1">
					</span>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(3)">
					<a href="#default-tab-3" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 3) ? "active" : ""; ?>">
						Home SUPERIOR 0.5
						<span id="porc_acierto_home_marca_badge" class="badge bg-primary rounded-0 fs-12px ms-1">
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(20)">
					<a href="#default-tab-20" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 20) ? "active" : ""; ?>">
						Home SUPERIOR 1.5
						<span id="porc_acierto_home_superior_1_5_badge" class="badge bg-primary rounded-0 fs-12px ms-1">
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(4)">
					<a href="#default-tab-4" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 4) ? "active" : ""; ?>">
						Away SUPERIOR 0.5
						<span id="porc_acierto_away_marca_badge" class="badge bg-primary rounded-0 fs-12px ms-1">
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(21)">
					<a href="#default-tab-21" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 21) ? "active" : ""; ?>">
						Away SUPERIOR 1.5
						<span id="porc_acierto_away_superior_1_5_badge" class="badge bg-primary rounded-0 fs-12px ms-1">
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(5)">
					<a href="#default-tab-5" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 5) ? "active" : ""; ?>">
						Ambos marcan
						<span id="porc_acierto_ambos_marcan_badge" class="badge bg-primary rounded-0 fs-12px ms-1">
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(6)">
					<a href="#default-tab-6" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 6) ? "active" : ""; ?>">
						Apuesta custom
					</a>
				</li>
			</ul>
			<div class="tab-content panel rounded-0 rounded-bottom">
				<?php #region region TAB total_goles_masde_1_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">
					<?php #region region TABLE total_goles_masde_1_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="8" class="text-center">5 partidos</th>
							<th colspan="8" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Goals<br>Home</th>
							<th class="text-center">Avg Conceded<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Goals<br>Away</th>
							<th class="text-center">Avg Conceded<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Goals<br>Home</th>
							<th class="text-center bg-gray-900">Avg Conceded<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Goals<br>Away</th>
							<th class="text-center bg-gray-900">Avg Conceded<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_1_5"><?php echo $prob_total_goles_masde_1_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_1_5"><?php echo $prob_total_goles_masde_1_5_p1_home; ?>%</td>
							<td class="text-center value_num_1_5"><?php echo number_format($avg_goals_p1_home, 2); ?></td>
							<td class="text-center value_num_1_5"><?php echo number_format($avg_conceded_p1_home, 2); ?></td>
							<td class="text-center value_porc_1_5"><?php echo $prob_total_goles_masde_1_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_1_5"><?php echo $prob_total_goles_masde_1_5_p1_away; ?>%</td>
							<td class="text-center value_num_1_5"><?php echo number_format($avg_goals_p1_away, 2); ?></td>
							<td class="text-center value_num_1_5"><?php echo number_format($avg_conceded_p1_away, 2); ?></td>
							<td class="text-center value_porc_1_5 bg-gray-900"><?php echo $prob_total_goles_masde_1_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_1_5 bg-gray-900"><?php echo $prob_total_goles_masde_1_5_p2_home; ?>%</td>
							<td class="text-center value_num_1_5 bg-gray-900"><?php echo number_format($avg_goals_p2_home, 2); ?></td>
							<td class="text-center value_num_1_5 bg-gray-900"><?php echo number_format($avg_conceded_p2_home, 2); ?></td>
							<td class="text-center value_porc_1_5 bg-gray-900"><?php echo $prob_total_goles_masde_1_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_1_5 bg-gray-900"><?php echo $prob_total_goles_masde_1_5_p2_away; ?>%</td>
							<td class="text-center value_num_1_5 bg-gray-900"><?php echo number_format($avg_goals_p2_away, 2); ?></td>
							<td class="text-center value_num_1_5 bg-gray-900"><?php echo number_format($avg_conceded_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table total_goles_masde_1_5 ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_1_5" name="porc_acierto_1_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_total_goles_masde_1_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_agregar_prob_total_goles_masde_1_5" name="sub_agregar_prob_total_goles_masde_1_5" class="btn btn-xs btn-success w-100 no-border-radious">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_total_goles_masde_1_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB total_goles_masde_1_5 ?>
				<?php #region region TAB home_marca ?>
				<div class="tab-pane fade <?php echo ($tabselected == 3) ? "active show" : ""; ?>" id="default-tab-3">
					<?php #region region TABLE home_marca ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="4" class="text-center">5 partidos</th>
							<th colspan="4" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Goals<br>Home</th>
							<th class="text-center">Avg Conceded<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Goals<br>Home</th>
							<th class="text-center bg-gray-900">Avg Conceded<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_home_marca"><?php echo $prob_home_marca_p1_home_h; ?>%</td>
							<td class="text-center value_porc_home_marca"><?php echo $prob_home_marca_p1_home; ?>%</td>
							<td class="text-center value_num_home_marca"><?php echo number_format($avg_goals_p1_home, 2); ?></td>
							<td class="text-center value_num_home_marca"><?php echo number_format($avg_conceded_p1_away, 2); ?></td>
							<td class="text-center value_porc_home_marca bg-gray-900"><?php echo $prob_home_marca_p2_home_h; ?>%</td>
							<td class="text-center value_porc_home_marca bg-gray-900"><?php echo $prob_home_marca_p2_home; ?>%</td>
							<td class="text-center value_num_home_marca bg-gray-900"><?php echo number_format($avg_goals_p2_home, 2); ?></td>
							<td class="text-center value_num_home_marca bg-gray-900"><?php echo number_format($avg_conceded_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table home_marca ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_home_marca" name="porc_acierto_home_marca" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_home_marca ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_agregar_prob_home_marca" name="sub_agregar_prob_home_marca" class="btn btn-xs btn-success w-100 no-border-radious">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_home_marca ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB home_marca ?>
				<?php #region region TAB home_superior_1_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected == 20) ? "active show" : ""; ?>" id="default-tab-20">
					<?php #region region TABLE home_superior_1_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="4" class="text-center">5 partidos</th>
							<th colspan="4" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Goals<br>Home</th>
							<th class="text-center">Avg Conceded<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Goals<br>Home</th>
							<th class="text-center bg-gray-900">Avg Conceded<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_home_superior_1_5"><?php echo $prob_home_superior_1_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_home_superior_1_5"><?php echo $prob_home_superior_1_5_p1_home; ?>%</td>
							<td class="text-center value_num_home_superior_1_5"><?php echo number_format($avg_goals_p1_home, 2); ?></td>
							<td class="text-center value_num_home_conceded_superior_1_5"><?php echo number_format($avg_conceded_p1_away, 2); ?></td>
							<td class="text-center value_porc_home_superior_1_5 bg-gray-900"><?php echo $prob_home_superior_1_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_home_superior_1_5 bg-gray-900"><?php echo $prob_home_superior_1_5_p2_home; ?>%</td>
							<td class="text-center value_num_home_superior_1_5 bg-gray-900"><?php echo number_format($avg_goals_p2_home, 2); ?></td>
							<td class="text-center value_num_home_conceded_superior_1_5 bg-gray-900"><?php echo number_format($avg_conceded_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table home_superior_1_5 ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_home_superior_1_5" name="porc_acierto_home_superior_1_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_home_superior_1_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_agregar_prob_home_superior_1_5" name="sub_agregar_prob_home_superior_1_5" class="btn btn-xs btn-success w-100 no-border-radious">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_home_superior_1_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB home_superior_1_5 ?>
				<?php #region region TAB away_marca ?>
				<div class="tab-pane fade <?php echo ($tabselected == 4) ? "active show" : ""; ?>" id="default-tab-4">
					<?php #region region TABLE away_marca ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="4" class="text-center">5 partidos</th>
							<th colspan="4" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Goals<br>Away</th>
							<th class="text-center">Avg Conceded<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Goals<br>Away</th>
							<th class="text-center bg-gray-900">Avg Conceded<br>Home</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_away_marca"><?php echo $prob_away_marca_p1_away_a; ?>%</td>
							<td class="text-center value_porc_away_marca"><?php echo $prob_away_marca_p1_away; ?>%</td>
							<td class="text-center value_num_away_marca"><?php echo number_format($avg_goals_p1_away, 2); ?></td>
							<td class="text-center value_num_away_marca"><?php echo number_format($avg_conceded_p1_home, 2); ?></td>
							<td class="text-center value_porc_away_marca bg-gray-900"><?php echo $prob_away_marca_p2_away_a; ?>%</td>
							<td class="text-center value_porc_away_marca bg-gray-900"><?php echo $prob_away_marca_p2_away; ?>%</td>
							<td class="text-center value_num_away_marca bg-gray-900"><?php echo number_format($avg_goals_p2_away, 2); ?></td>
							<td class="text-center value_num_away_marca bg-gray-900"><?php echo number_format($avg_conceded_p2_home, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table away_marca ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_away_marca" name="porc_acierto_away_marca" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_away_marca ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_agregar_prob_away_marca" name="sub_agregar_prob_away_marca" class="btn btn-xs btn-success w-100 no-border-radious">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_away_marca ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB away_marca ?>
				<?php #region region TAB away_superior_1_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected == 21) ? "active show" : ""; ?>" id="default-tab-21">
					<?php #region region TABLE away_superior_1_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="4" class="text-center">5 partidos</th>
							<th colspan="4" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Goals<br>Away</th>
							<th class="text-center">Avg Conceded<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Goals<br>Away</th>
							<th class="text-center bg-gray-900">Avg Conceded<br>Home</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_away_superior_1_5"><?php echo $prob_away_superior_1_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_away_superior_1_5"><?php echo $prob_away_superior_1_5_p1_away; ?>%</td>
							<td class="text-center value_num_away_superior_1_5"><?php echo number_format($avg_goals_p1_away, 2); ?></td>
							<td class="text-center value_num_away_conceded_superior_1_5"><?php echo number_format($avg_conceded_p1_home, 2); ?></td>
							<td class="text-center value_porc_away_superior_1_5 bg-gray-900"><?php echo $prob_away_superior_1_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_away_superior_1_5 bg-gray-900"><?php echo $prob_away_superior_1_5_p2_away; ?>%</td>
							<td class="text-center value_num_away_superior_1_5 bg-gray-900"><?php echo number_format($avg_goals_p2_away, 2); ?></td>
							<td class="text-center value_num_away_conceded_superior_1_5 bg-gray-900"><?php echo number_format($avg_conceded_p2_home, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table away_superior_1_5 ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_away_superior_1_5" name="porc_acierto_away_superior_1_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_away_superior_1_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_agregar_prob_away_superior_1_5" name="sub_agregar_prob_away_superior_1_5" class="btn btn-xs btn-success w-100 no-border-radious">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_away_superior_1_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB away_superior_1_5 ?>
				<?php #region region TAB ambos_marcan ?>
				<div class="tab-pane fade <?php echo ($tabselected == 5) ? "active show" : ""; ?>" id="default-tab-5">
					<?php #region region TABLE ambos_marcan ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="8" class="text-center">5 partidos</th>
							<th colspan="8" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Goals<br>Home</th>
							<th class="text-center">Avg Conceded<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Goals<br>Away</th>
							<th class="text-center">Avg Conceded<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Goals<br>Home</th>
							<th class="text-center bg-gray-900">Avg Conceded<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Goals<br>Away</th>
							<th class="text-center bg-gray-900">Avg Conceded<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_ambos_marcan"><?php echo $prob_ambos_marcan_p1_home_h; ?>%</td>
							<td class="text-center value_porc_ambos_marcan"><?php echo $prob_ambos_marcan_p1_home; ?>%</td>
							<td class="text-center value_num_ambos_marcan"><?php echo number_format($avg_goals_p1_home, 2); ?></td>
							<td class="text-center value_num_ambos_marcan"><?php echo number_format($avg_conceded_p1_home, 2); ?></td>
							<td class="text-center value_porc_ambos_marcan"><?php echo $prob_ambos_marcan_p1_away_a; ?>%</td>
							<td class="text-center value_porc_ambos_marcan"><?php echo $prob_ambos_marcan_p1_away; ?>%</td>
							<td class="text-center value_num_ambos_marcan"><?php echo number_format($avg_goals_p1_away, 2); ?></td>
							<td class="text-center value_num_ambos_marcan"><?php echo number_format($avg_conceded_p1_away, 2); ?></td>
							<td class="text-center value_porc_ambos_marcan bg-gray-900"><?php echo $prob_ambos_marcan_p2_home_h; ?>%</td>
							<td class="text-center value_porc_ambos_marcan bg-gray-900"><?php echo $prob_ambos_marcan_p2_home; ?>%</td>
							<td class="text-center value_num_ambos_marcan bg-gray-900"><?php echo number_format($avg_goals_p2_home, 2); ?></td>
							<td class="text-center value_num_ambos_marcan bg-gray-900"><?php echo number_format($avg_conceded_p2_home, 2); ?></td>
							<td class="text-center value_porc_ambos_marcan bg-gray-900"><?php echo $prob_ambos_marcan_p2_away_a; ?>%</td>
							<td class="text-center value_porc_ambos_marcan bg-gray-900"><?php echo $prob_ambos_marcan_p2_away; ?>%</td>
							<td class="text-center value_num_ambos_marcan bg-gray-900"><?php echo number_format($avg_goals_p2_away, 2); ?></td>
							<td class="text-center value_num_ambos_marcan bg-gray-900"><?php echo number_format($avg_conceded_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table ambos_marcan ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_ambos_marcan" name="porc_acierto_ambos_marcan" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_ambos_marcan ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_agregar_prob_ambos_marcan" name="sub_agregar_prob_ambos_marcan" class="btn btn-xs btn-success w-100 no-border-radious">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_ambos_marcan ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB ambos_marcan ?>
				<?php #region region TAB apuesta custom ?>
				<div class="tab-pane fade <?php echo ($tabselected == 6) ? "active show" : ""; ?>" id="default-tab-6">
					<!-- BEGIN row -->
					<div class="row mt-3">
						<!-- BEGIN text -->
						<div class="col-md-6 col-xs-12">
							<div class="input-group">
			                    <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
			                        Tipo apuesta:
			                    </span>
								<input type="text" name="tipo_apuesta_custom" id="tipo_apuesta_custom" class="form-control form-control-fh fs-12px no-border-radious "/>
							</div>
						</div>
						<!-- END text -->
						<!-- BEGIN text -->
						<div class="col-md-6 col-xs-12">
							<div class="input-group">
			                    <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
			                        % acierto:
			                    </span>
								<input type="text" name="porc_acierto_custom" id="porc_acierto_custom" class="form-control form-control-fh fs-12px no-border-radious "/>
							</div>
						</div>
						<!-- END text -->
					</div>
					<!-- END row -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_custom ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_agregar_custom" name="sub_agregar_custom" class="btn btn-xs btn-success w-100 no-border-radious">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_custom ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB apuesta custom ?>
			</div>
			<?php #endregion NAVTAB analisis ?>
			<!-- BEGIN row -->
			<div class="row mt-3">
				<?php #region region SUBMIT sub_marcar_revisado_probabilidades ?>
				<div class="col-md-12 col-xs-12">
					<button type="submit" id="sub_marcar_revisado_probabilidades" name="sub_marcar_revisado_probabilidades" class="btn btn-xs btn-success w-100 no-border-radious">
						Marcar como revisado
					</button>
				</div>
				<?php #endregion SUBMIT sub_marcar_revisado_probabilidades ?>
			</div>
			<!-- END row -->
			<?php #region region PANEL partidos_infos ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">
						Historico
						<span class="badge bg-primary rounded-0 fs-10px" id="historico-counter">
                            <?php echo count($partidos_infos); ?>
                        </span>
					</h4>
				</div>
				<!-- BEGIN filter buttons -->
				<div class="panel-body p-2">
					<div class="row mb-2">
						<div class="col-12">
							<div class="btn-group btn-group-toggle w-100" data-toggle="buttons" id="historico-filters">
								<label class="btn btn-secondary active" data-filter="todos">
									<input type="radio" name="historico_filter" autocomplete="off" value="todos" checked> Todos
								</label>
								<label class="btn btn-secondary" data-filter="home">
									<input type="radio" name="historico_filter" autocomplete="off" value="home"> Home
								</label>
								<label class="btn btn-secondary" data-filter="home_h">
									<input type="radio" name="historico_filter" autocomplete="off" value="home_h"> Home @H
								</label>
								<label class="btn btn-secondary" data-filter="home_a">
									<input type="radio" name="historico_filter" autocomplete="off" value="home_a"> Home @A
								</label>
								<label class="btn btn-secondary" data-filter="away">
									<input type="radio" name="historico_filter" autocomplete="off" value="away"> Away
								</label>
								<label class="btn btn-secondary" data-filter="away_a">
									<input type="radio" name="historico_filter" autocomplete="off" value="away_a"> Away @A
								</label>
								<label class="btn btn-secondary" data-filter="away_h">
									<input type="radio" name="historico_filter" autocomplete="off" value="away_h"> Away @H
								</label>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-12">
							<button type="button" class="btn btn-xs btn-success" id="export-excel-btn">
								<i class="fa fa-file-excel"></i> Export Excel
							</button>
						</div>
					</div>
				</div>
				<!-- END filter buttons -->
				<!-- BEGIN PANEL body -->
				<div class="p-1 table-nowrap" style="overflow: auto; height: 300px">
					<?php #region region TABLE partidos_infos ?>
					<table id="historico-table" class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="text-center">#</th>
							<th class="text-center">Fecha</th>
							<th class="text-center">Days Ago</th>
							<th class="text-center">Home</th>
							<th class="text-center">Away</th>
							<th class="text-center">Torneo</th>
							<th class="text-center">Temporada</th>
							<th class="text-center">Home<br>goals</th>
							<th class="text-center">Away<br>goals</th>
						</tr>
						</thead>
						<tbody class="fs-10px">
						<?php foreach ($partidos_infos as $partido_info): ?>
							<?php
							$current_row = $n_row_partidos_infos;
							$separator_class = '';
							if ($current_row == 5) {
								$separator_class = ' historical-separator-5';
							} elseif ($current_row == 10) {
								$separator_class = ' historical-separator-10';
							}

							// Calculate days ago using Bogotá timezone
							setTimeZoneCol(); // Set Bogotá timezone
							$fecha_actual = create_date();
							$days_ago = '';
							if (!empty($partido_info->fecha)) {
								$days_diff = getDateDiffDaysNonLiteral($partido_info->fecha, $fecha_actual);
								$days_ago = abs($days_diff); // Always show positive number for "days ago"
							}
							?>
							<tr class="cursor-pointer<?php echo $separator_class; ?>">
								<td class="text-center"><?php echo $n_row_partidos_infos++; ?></td>
								<td class="text-center"><?php echo $partido_info->fecha; ?></td>
								<td class="text-center"><?php echo $days_ago; ?></td>
								<td class="text-center"><?php echo $partido_info->home; ?></td>
								<td class="text-center"><?php echo $partido_info->away; ?></td>
								<td class="text-center"><?php echo $partido_info->nom_pais; ?></td>
								<td class="text-center"><?php echo $partido_info->season; ?></td>
								<td class="text-center"><?php echo $partido_info->homegoals; ?></td>
								<td class="text-center"><?php echo $partido_info->awaygoals; ?></td>
							</tr>
						<?php endforeach; ?>
						</tbody>
					</table>
					<?php #endregion TABLE partidos_infos ?>
				</div>
				<!-- END PANEL body -->
			</div>
			<?php #endregion PANEL partidos_infos ?>
		</form>
		<?php #endregion FORM ?>

		<!-- BEGIN Upload Torneo Modal -->
		<div class="modal fade" id="uploadTorneoModal" tabindex="-1" aria-labelledby="uploadTorneoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="uploadTorneoModalLabel">
							<i class="fas fa-upload me-2"></i>Cargar datos del torneo
						</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<form action="epartido_probabilidades" method="POST" enctype="multipart/form-data">
						<input type="hidden" id="id_partido_modal" name="id_partido" value="<?php echo @recover_var($id_partido) ?>">
						<input type="hidden" id="tabselected_modal" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">
						<input type="hidden" id="pais_upload_id" name="pais_upload_id" value="">

						<div class="modal-body">
							<!-- File Upload -->
							<div class="row mb-3">
								<div class="col-12">
									<label for="archivocsv_modal" class="form-label">Archivo CSV:</label>
									<input type="file" class="form-control" id="archivocsv_modal" name="archivocsv" accept=".csv" required>
									<div class="form-text text-muted">Seleccione un archivo CSV con los datos del torneo.</div>
								</div>
							</div>
							
							<!-- Tournament Display -->
							<div class="row mb-3">
								<div class="col-12">
									<label class="form-label">Torneo:</label>
									<div class="form-control bg-secondary text-white" id="torneo_display" readonly></div>
								</div>
							</div>

							<!-- Season Input -->
							<div class="row mb-3">
								<div class="col-12">
									<label for="season_upload" class="form-label">Temporada:</label>
									<input type="text" class="form-control" id="season_upload" name="season_upload" required>
								</div>
							</div>
						</div>

						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" name="sub_upload_torneo" class="btn btn-success">
								<i class="fas fa-upload me-1"></i>Subir archivo
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- END Upload Torneo Modal -->

		<!-- BEGIN Create Tournament Modal -->
		<div class="modal fade" id="createTournamentModal" tabindex="-1" aria-labelledby="createTournamentModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="createTournamentModalLabel">
							<i class="fas fa-plus me-2"></i>Create New Tournament
						</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<form id="createTournamentForm">
						<div class="modal-body">
							<div class="row mb-3">
								<div class="col-12">
									<label for="tournament_name_create" class="form-label">Tournament Name:</label>
									<input type="text" class="form-control" id="tournament_name_create" name="tournament_name" required>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
							<button type="submit" class="btn btn-success">
								<i class="fas fa-save me-1"></i>Create Tournament
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- END Create Tournament Modal -->

		<!-- BEGIN Edit Tournament Modal -->
		<div class="modal fade" id="editTournamentModal" tabindex="-1" aria-labelledby="editTournamentModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="editTournamentModalLabel">
							<i class="fas fa-edit me-2"></i>Edit Tournament
						</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<form id="editTournamentForm">
						<input type="hidden" id="tournament_id_edit" name="tournament_id">
						<div class="modal-body">
							<div class="row mb-3">
								<div class="col-12">
									<label for="tournament_name_edit" class="form-label">Tournament Name:</label>
									<input type="text" class="form-control" id="tournament_name_edit" name="tournament_name" required>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
							<button type="submit" class="btn btn-success">
								<i class="fas fa-save me-1"></i>Update Tournament
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- END Edit Tournament Modal -->
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<?php #region region js select2 ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/select2/dist/js/select2.min.js"></script>

<script type="text/javascript">
    $(".default-select2").select2();
    
    // Focus the search input when the dropdown is opened
    $('#id_pais').on('select2:open', function () {
        document.querySelector('.select2-search__field').focus();
    });
</script>
<?php #endregion js select2 ?>
<?php #region region JS tabselect ?>
<script type="text/javascript">
    function tabselect(ntab) {
        document.getElementById('tabselected').value = ntab;
    }
</script>
<?php #endregion JS tabselect ?>

<?php #region region JS colorear un TD si el contenido cumple una condicion y calcular % acierto ?>
<script>
    //resaltar las celdas que tengan -1 para indicar que faltan partidos para los calculos de probabilidades
    document.querySelectorAll('.text-center').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value === -1) {
            cell.classList.add('text-warning');
        }
    });
    
    let total_count_1_5               = 0;
    let total_count_home_marca        = 0;
    let total_count_home_superior_1_5 = 0;
    let total_count_away_marca        = 0;
    let total_count_away_superior_1_5 = 0;
    let total_count_ambos_marcan      = 0;
    let total_criterios               = 16;
    let total_criterios_team_marca    = 8;
    
    document.querySelectorAll('.value_porc_1_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_1_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 1.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_1_5++; // Increment the total counter
        }
    });
    
    document.querySelectorAll('.value_porc_home_marca').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_home_marca++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_home_marca').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 1.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_home_marca++; // Increment the total counter
        }
    });
    
    document.querySelectorAll('.value_porc_home_superior_1_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_home_superior_1_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_home_superior_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 2.50) {
            cell.classList.add('text-success');
            total_count_home_superior_1_5++;
        }
    });
    document.querySelectorAll('.value_num_home_conceded_superior_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 2.00) {
            cell.classList.add('text-success');
            total_count_home_superior_1_5++;
        }
    });
    
    document.querySelectorAll('.value_porc_away_marca').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_marca++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_away_marca').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 1.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_marca++; // Increment the total counter
        }
    });
    
    document.querySelectorAll('.value_porc_away_superior_1_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_superior_1_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_away_superior_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 2.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_superior_1_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_away_conceded_superior_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 2.00) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_superior_1_5++; // Increment the total counter
        }
    });
    
    document.querySelectorAll('.value_porc_ambos_marcan').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_ambos_marcan++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_ambos_marcan').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 1.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_ambos_marcan++; // Increment the total counter
        }
    });
    
    let porcentaje_acierto_1_5               = Math.round((total_count_1_5 * 100) / total_criterios);
    let porcentaje_acierto_home_marca        = Math.round((total_count_home_marca * 100) / total_criterios_team_marca);
    let porcentaje_acierto_home_superior_1_5 = Math.round((total_count_home_superior_1_5 * 100) / total_criterios_team_marca);
    let porcentaje_acierto_away_marca        = Math.round((total_count_away_marca * 100) / total_criterios_team_marca);
    let porcentaje_acierto_away_superior_1_5 = Math.round((total_count_away_superior_1_5 * 100) / total_criterios_team_marca);
    let porcentaje_acierto_ambos_marcan      = Math.round((total_count_ambos_marcan * 100) / total_criterios);
    
    // Assign the value to the input field
    document.getElementById('porc_acierto_1_5').value                         = porcentaje_acierto_1_5;
    document.getElementById('porc_acierto_1_5_badge').innerText               = porcentaje_acierto_1_5 + '%';
    document.getElementById('porc_acierto_home_marca').value                  = porcentaje_acierto_home_marca;
    document.getElementById('porc_acierto_home_marca_badge').innerText        = porcentaje_acierto_home_marca + '%';
    document.getElementById('porc_acierto_home_superior_1_5').value           = porcentaje_acierto_home_superior_1_5;
    document.getElementById('porc_acierto_home_superior_1_5_badge').innerText = porcentaje_acierto_home_superior_1_5 + '%';
    document.getElementById('porc_acierto_away_marca').value                  = porcentaje_acierto_away_marca;
    document.getElementById('porc_acierto_away_marca_badge').innerText        = porcentaje_acierto_away_marca + '%';
    document.getElementById('porc_acierto_away_superior_1_5').value           = porcentaje_acierto_away_superior_1_5;
    document.getElementById('porc_acierto_away_superior_1_5_badge').innerText = porcentaje_acierto_away_superior_1_5 + '%';
    document.getElementById('porc_acierto_ambos_marcan').value                = porcentaje_acierto_ambos_marcan;
    document.getElementById('porc_acierto_ambos_marcan_badge').innerText      = porcentaje_acierto_ambos_marcan + '%';
</script>
<?php #endregion JS colorear un TD si el contenido cumple una condicion y calcular % acierto ?>

<?php #region region JS upload modal ?>
<script type="text/javascript">
    function openUploadModal(paisId, paisNombre, season) {
        // Set hidden field values
        document.getElementById('pais_upload_id').value = paisId;

        // Set display values
        document.getElementById('torneo_display').textContent = paisNombre;
        document.getElementById('season_upload').value = season;

        // Clear file input
        document.getElementById('archivocsv_modal').value = '';

        // Show modal
        var modal = new bootstrap.Modal(document.getElementById('uploadTorneoModal'));
        modal.show();
    }
</script>
<?php #endregion JS upload modal ?>

<?php #region region JS countdown ?>
<script type="text/javascript">
    function updateCountdown() {
        <?php if (!empty($cur_partido->fecha) && !empty($cur_partido->horamilitar)): ?>
            // Create the target date using Bogotá timezone
            const targetDateStr = '<?php echo $cur_partido->fecha . ' ' . $cur_partido->horamilitar; ?>';
            const targetDate = new Date(targetDateStr);

            // Get current time in Bogotá timezone
            const now = new Date();
            const bogotaOffset = -5 * 60; // Bogotá is UTC-5
            const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
            const bogotaTime = new Date(utc + (bogotaOffset * 60000));

            // Calculate the difference
            const timeDiff = targetDate.getTime() - bogotaTime.getTime();

            if (timeDiff > 0) {
                // Calculate days, hours, and minutes
                const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

                // Format countdown text
                let countdownText = '';
                if (days > 0) {
                    countdownText += days + ' day' + (days !== 1 ? 's' : '') + ', ';
                }
                if (hours > 0 || days > 0) {
                    countdownText += hours + ' hour' + (hours !== 1 ? 's' : '') + ', ';
                }
                countdownText += minutes + ' minute' + (minutes !== 1 ? 's' : '');

                // Show countdown
                document.getElementById('countdown_text').textContent = countdownText;
                document.getElementById('countdown_container').style.display = 'inline-block';
            } else {
                // Hide countdown if date is in the past
                document.getElementById('countdown_container').style.display = 'none';
            }
        <?php endif; ?>
    }

    // Update countdown immediately and then every minute
    updateCountdown();
    setInterval(updateCountdown, 60000); // Update every minute
</script>
<?php #endregion JS countdown ?>

<?php #endregion JS ?>

<?php #region region JS Tournament Management ?>
<script type="text/javascript">
	// Show/hide tournament dropdown
	function showAddTournamentDropdown() {
		document.getElementById('tournament_dropdown_container').style.display = 'block';
		// Initialize select2 for the dropdown if not already initialized
		if (!$('#id_pais_dropdown').hasClass('select2-hidden-accessible')) {
			$('#id_pais_dropdown').select2();
		}
	}

	function hideAddTournamentDropdown() {
		document.getElementById('tournament_dropdown_container').style.display = 'none';
		$('#id_pais_dropdown').val('').trigger('change');
	}

	// Add selected tournament
	function addSelectedTournament() {
		const selectedPaisId = document.getElementById('id_pais_dropdown').value;
		if (selectedPaisId) {
			// Create a form and submit it
			const form = document.createElement('form');
			form.method = 'POST';
			form.action = 'epartido_probabilidades';

			// Add hidden fields
			const idPartidoField = document.createElement('input');
			idPartidoField.type = 'hidden';
			idPartidoField.name = 'id_partido';
			idPartidoField.value = document.getElementById('id_partido').value;
			form.appendChild(idPartidoField);

			const tabSelectedField = document.createElement('input');
			tabSelectedField.type = 'hidden';
			tabSelectedField.name = 'tabselected';
			tabSelectedField.value = document.getElementById('tabselected').value;
			form.appendChild(tabSelectedField);

			const idPaisField = document.createElement('input');
			idPaisField.type = 'hidden';
			idPaisField.name = 'id_pais';
			idPaisField.value = selectedPaisId;
			form.appendChild(idPaisField);

			document.body.appendChild(form);
			form.submit();
		} else {
			alert('Please select a tournament to add.');
		}
	}

	// Show create tournament modal
	function showCreateTournamentModal() {
		$('#createTournamentModal').modal('show');
		// Auto-focus the tournament name field when modal is shown
		$('#createTournamentModal').on('shown.bs.modal', function () {
			$('#tournament_name_create').focus();
		});
	}

	// Show edit tournament modal
	function openEditTournamentModal(tournamentId, tournamentName) {
		document.getElementById('tournament_id_edit').value = tournamentId;
		document.getElementById('tournament_name_edit').value = tournamentName;
		$('#editTournamentModal').modal('show');
		// Auto-focus the tournament name field when modal is shown
		$('#editTournamentModal').on('shown.bs.modal', function () {
			$('#tournament_name_edit').focus().select();
		});
	}

	// Handle create tournament form submission
	document.getElementById('createTournamentForm').addEventListener('submit', function(e) {
		e.preventDefault();

		const formData = new FormData();
		formData.append('sub_create_tournament', '1');
		formData.append('tournament_name', document.getElementById('tournament_name_create').value);
		formData.append('id_partido', document.getElementById('id_partido').value);
		formData.append('tabselected', document.getElementById('tabselected').value);

		fetch('epartido_probabilidades', {
			method: 'POST',
			body: formData
		})
		.then(response => response.text())
		.then(data => {
			// Close the modal and reload the page
			$('#createTournamentModal').modal('hide');
			location.reload();
		})
		.catch(error => {
			console.error('Error:', error);
			alert('Error creating tournament. Please try again.');
		});
	});

	// Handle edit tournament form submission
	document.getElementById('editTournamentForm').addEventListener('submit', function(e) {
		e.preventDefault();

		const formData = new FormData();
		formData.append('sub_edit_tournament', '1');
		formData.append('tournament_id', document.getElementById('tournament_id_edit').value);
		formData.append('tournament_name', document.getElementById('tournament_name_edit').value);
		formData.append('id_partido', document.getElementById('id_partido').value);
		formData.append('tabselected', document.getElementById('tabselected').value);

		fetch('epartido_probabilidades', {
			method: 'POST',
			body: formData
		})
		.then(response => response.text())
		.then(data => {
			// Close the modal and reload the page
			$('#editTournamentModal').modal('hide');
			location.reload();
		})
		.catch(error => {
			console.error('Error:', error);
			alert('Error updating tournament. Please try again.');
		});
	});
</script>
<?php #endregion JS Tournament Management ?>

<?php #region region JS grouped controls ?>
<script src="<?php echo RUTA ?>resources/js/grouped_controls.js"></script>
<?php #endregion JS grouped controls ?>



<?php #region region JS Historico Filtering ?>
<script type="text/javascript">
	$(document).ready(function() {
		// Store original data and current partido info
		const originalRows = [];
		const partidoHome = '<?php echo addslashes($cur_partido->home); ?>';
		const partidoAway = '<?php echo addslashes($cur_partido->away); ?>';

		// Store original table rows
		$('#historico-table tbody tr').each(function(index) {
			const row = $(this);
			const partidoInfoHome = row.find('td:nth-child(4)').text().trim(); // Updated column index (Home is now 4th column)
			const partidoInfoAway = row.find('td:nth-child(5)').text().trim(); // Updated column index (Away is now 5th column)

			originalRows.push({
				element: row.clone(),
				home: partidoInfoHome,
				away: partidoInfoAway,
				rowNumber: index + 1,
				separatorClass: row.hasClass('historical-separator-5') ? 'historical-separator-5' :
							   (row.hasClass('historical-separator-10') ? 'historical-separator-10' : '')
			});
		});

		// Filter function
		function filterHistorico(filterType) {
			let filteredRows = [];

			originalRows.forEach(function(rowData) {
				let showRow = false;

				switch(filterType) {
					case 'todos':
						showRow = true;
						break;
					case 'home':
						// Home: Show records where PartidoInfo.home OR PartidoInfo.away = Partido.home
						showRow = (rowData.home === partidoHome || rowData.away === partidoHome);
						break;
					case 'home_h':
						// Home @H: Show records where PartidoInfo.home = Partido.home
						showRow = (rowData.home === partidoHome);
						break;
					case 'home_a':
						// Home @A: Show records where PartidoInfo.away = Partido.home
						showRow = (rowData.away === partidoHome);
						break;
					case 'away':
						// Away: Show records where PartidoInfo.away OR PartidoInfo.home = Partido.away
						showRow = (rowData.away === partidoAway || rowData.home === partidoAway);
						break;
					case 'away_a':
						// Away @A: Show records where PartidoInfo.away = Partido.away
						showRow = (rowData.away === partidoAway);
						break;
					case 'away_h':
						// Away @H: Show records where PartidoInfo.home = Partido.away
						showRow = (rowData.home === partidoAway);
						break;
				}

				if (showRow) {
					filteredRows.push(rowData);
				}
			});

			// Clear current table
			$('#historico-table tbody').empty();

			// Add filtered rows with correct numbering and separators
			filteredRows.forEach(function(rowData, index) {
				const newRow = rowData.element.clone();

				// Update row number
				newRow.find('td:first-child').text(index + 1);

				// Remove old separator classes
				newRow.removeClass('historical-separator-5 historical-separator-10');

				// Add separator classes at positions 5 and 10
				if (index + 1 === 5) {
					newRow.addClass('historical-separator-5');
				} else if (index + 1 === 10) {
					newRow.addClass('historical-separator-10');
				}

				$('#historico-table tbody').append(newRow);
			});

			// Update counter
			$('#historico-counter').text(filteredRows.length);
		}

		// Handle filter button clicks
		$('#historico-filters label').on('click', function() {
			const filterType = $(this).data('filter');
			filterHistorico(filterType);
		});

		// Excel export functionality
		$('#export-excel-btn').on('click', function() {
			exportToExcel();
		});

		function exportToExcel() {
			// Get current filter (fallback to 'todos' if none active)
			let activeFilter = $('#historico-filters label.active').data('filter');
			if (!activeFilter) { activeFilter = 'todos'; }

			// Show loading state
			$('#export-excel-btn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Exporting...');

			// Create form data for POST request
			const formData = new FormData();
			formData.append('id_partido', '<?php echo $cur_partido->id; ?>');
			formData.append('filter_type', activeFilter);
			formData.append('partido_home', partidoHome);
			formData.append('partido_away', partidoAway);

			// Create XMLHttpRequest for file download
			const xhr = new XMLHttpRequest();
			xhr.open('POST', '<?php echo RUTA; ?>src/export_historico_excel.php', true);
			xhr.responseType = 'blob';
			// Optional timeout to avoid indefinite hanging
			xhr.timeout = 120000; // 120s

			xhr.onload = function() {
				try {
					if (xhr.status === 200) {
						// Create download link with correct XLSX MIME and sanitized filename
						const blob = new Blob([xhr.response], {
							type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
						});
						const url = window.URL.createObjectURL(blob);
						const link = document.createElement('a');
						link.href = url;
						const unsafeName = 'historico_' + partidoHome + '_vs_' + partidoAway + '_' + activeFilter + '.xlsx';
						const safeName = unsafeName.replace(/[\\/:*?"<>|]/g, '_');
						link.download = safeName;
						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
						window.URL.revokeObjectURL(url);

						// Show success message
						toastr.success('Excel file downloaded successfully');
					} else {
						// Attempt to read error message if provided
						toastr.error('Error generating Excel file');
						console.error('Export error:', xhr.status, xhr.statusText);
					}
				} catch (e) {
					console.error('Export exception:', e);
					toastr.error('Unexpected error during export');
				} finally {
					// Reset button state
					$('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
				}
			};

			xhr.onerror = function() {
				toastr.error('Network error occurred while exporting');
				$('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
			};

			xhr.onabort = function() {
				toastr.warning('Export aborted');
				$('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
			};

			xhr.ontimeout = function() {
				toastr.error('Export timed out');
				$('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
			};

			xhr.send(formData);
		}
	});
</script>
<?php #endregion JS Historico Filtering ?>

</body>
</html>