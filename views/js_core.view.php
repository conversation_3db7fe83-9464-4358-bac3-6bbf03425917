<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<script src="<?php echo RUTA ?>resources/js/fab.js"></script>

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
	<script type="text/javascript">
        swal({
            text   : "<?php echo $success_text; ?>",
            icon   : 'success',
            buttons: {
                confirm: {
                    text      : 'Ok',
                    value     : true,
                    visible   : true,
                    className : 'btn btn-success',
                    closeModal: true
                }
            }
        });
	</script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
	<script type="text/javascript">
        swal({
            text   : "<?php echo $error_text; ?>",
            icon   : 'error',
            buttons: {
                confirm: {
                    text      : 'Ok',
                    value     : true,
                    visible   : true,
                    className : 'btn btn-danger',
                    closeModal: true
                }
            }
        });
	</script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>
<?php #region region JS copy text and show tooltip copied ?>
<script>
    function copyAndShowTooltip(controlId) {
        const element = document.getElementById(controlId);

        if (element) {
            let textToCopy = '';

            // Handle different elements with selective text copying
            if (controlId === 'home' || controlId === 'away' || controlId === 'mdl_home_team' || controlId === 'mdl_away_team') {
                // For team names, extract only the actual team name without icons
                // Clone the element to avoid modifying the original
                const clonedElement = element.cloneNode(true);

                // Remove all icon elements (i tags with fa classes)
                const icons = clonedElement.querySelectorAll('i[class*="fa"]');
                icons.forEach(icon => icon.remove());

                // Get the cleaned text content and limit to first 20 characters
                textToCopy = clonedElement.textContent.trim().substring(0, 20).trim();
            } else {
                // For other elements, use cleaned textContent
                textToCopy = element.textContent.trim();
            }

            // Copy the cleaned text to the clipboard
            navigator.clipboard.writeText(textToCopy);

            // Create a tooltip element
            const tooltip       = document.createElement("div");
            tooltip.textContent = "Copied!";
            tooltip.classList.add("tooltip-copied");
            document.body.appendChild(tooltip);

            // Position the tooltip
            const rect         = element.getBoundingClientRect();
            tooltip.style.left = `${rect.left}px`;
            tooltip.style.top  = `${rect.top - 20}px`;

            // Show the tooltip for 1 second
            tooltip.style.opacity = 1;
            setTimeout(() => {
                tooltip.style.opacity = 0;
                setTimeout(() => {
                    tooltip.remove();
                }, 300); // Delay removal for smooth fading
            }, 1000);
        } else {
            console.error(`Element with ID "${controlId}" not found.`);
        }
    }
</script>
<?php #endregion JS copy text and show tooltip copied ?>
<?php #region region JS copy text of controls with specified class ?>
<script>
    function enableTextCopy() {
        document.querySelectorAll('.copyText').forEach(function (span) {
            span.addEventListener('click', function () {
                // Create a temporary input element to copy the span's text
                const tempInput = document.createElement('input');
                tempInput.value = this.textContent.trim();
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                
                // Show tooltip
                showTooltip(this, 'Copied!');
            });
        });
    }
    
    function showTooltip(element, message) {
        // Create tooltip element
        const tooltip       = document.createElement('div');
        tooltip.className   = 'tooltip-copied-text';
        tooltip.textContent = message;
        document.body.appendChild(tooltip);
        
        // Position the tooltip near the clicked element
        const rect         = element.getBoundingClientRect();
        tooltip.style.left = rect.left + window.scrollX + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
        tooltip.style.top  = rect.top + window.scrollY - tooltip.offsetHeight - 10 + 'px';
        
        // Show and hide tooltip
        tooltip.classList.add('show');
        setTimeout(() => {
            tooltip.classList.remove('show');
            document.body.removeChild(tooltip);
        }, 1500);
    }
</script>

<?php #endregion JS copy text of controls with specified class ?>
<?php #region region JS HIDE modal ?>
<script>
    function hideModal(modalId) {
        // Hide the modal
        $(modalId).modal('hide');
        
        // Manually remove the backdrop if it's still there
        setTimeout(function () {
            $('.modal-backdrop').remove();
        }, 500);
    }
</script>
<?php #endregion JS HIDE modal ?>
<?php #region region JS focus input text when modal shown ?>
<script>
    function focusTextWhenModalShown(modalId, inputTextId) {
        $(modalId).on('shown.bs.modal', function () {
            $(inputTextId).focus();
            $(inputTextId).select();
        });
    }
</script>
<?php #endregion JS focus input text when modal shown ?>
